# Weather Data API

This API allows weather stations to store weather data and retrieve historical weather information.

## Base URL
```
http://localhost:3000/api/data/weather
```

## Endpoints

### POST /api/data/weather
Store weather data from a weather station.

#### Request Body
```json
{
  "station_id": "weather_station_01",
  "timestamp": "2025-06-03T13:45:50.666Z",
  "temperature": 22.5,
  "humidity": 65.2,
  "pressure": 1013.25,
  "wind_speed": 5.8,
  "wind_direction": 180,
  "rainfall": 0.0,
  "solar_radiation": 850.5,
  "uv_index": 6.2,
  "battery_voltage": 12.8,
  "signal_strength": -65,
  "raw_data": "sensor_data_checksum_ok"
}
```

#### Field Descriptions
- `station_id` (string, optional): Unique identifier for the weather station
- `timestamp` (string, optional): ISO 8601 datetime. If not provided, current time is used
- `temperature` (number, optional): Temperature in Celsius
- `humidity` (number, optional): Relative humidity percentage (0-100)
- `pressure` (number, optional): Atmospheric pressure in hPa
- `wind_speed` (number, optional): Wind speed in m/s
- `wind_direction` (number, optional): Wind direction in degrees (0-360)
- `rainfall` (number, optional): Rainfall in mm
- `solar_radiation` (number, optional): Solar radiation in W/m²
- `uv_index` (number, optional): UV index
- `battery_voltage` (number, optional): Battery voltage in volts
- `signal_strength` (number, optional): Signal strength in dBm
- `raw_data` (string, optional): Raw sensor data for debugging

#### Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "timestamp": "2025-06-03T13:45:50.666Z",
    "station_id": "weather_station_01",
    "temperature": 22.5,
    "humidity": 65.2,
    "pressure": 1013.25,
    "wind_speed": 5.8,
    "wind_direction": 180,
    "rainfall": null,
    "solar_radiation": 850.5,
    "uv_index": 6.2,
    "battery_voltage": 12.8,
    "signal_strength": -65,
    "raw_data": null,
    "created_at": "2025-06-03 13:45:50"
  },
  "message": "Weather data stored successfully"
}
```

### GET /api/data/weather
Retrieve weather data with optional filtering and pagination.

#### Query Parameters
- `station_id` (string, optional): Filter by weather station ID
- `start_date` (string, optional): Filter records after this date (ISO 8601)
- `end_date` (string, optional): Filter records before this date (ISO 8601)
- `limit` (number, optional): Maximum number of records to return (1-1000, default: 100)
- `offset` (number, optional): Number of records to skip (default: 0)

#### Examples
```bash
# Get all weather data
GET /api/data/weather

# Get data from specific station
GET /api/data/weather?station_id=weather_station_01

# Get recent data with pagination
GET /api/data/weather?limit=10&offset=0

# Get data within date range
GET /api/data/weather?start_date=2025-06-01T00:00:00Z&end_date=2025-06-03T23:59:59Z
```

#### Response
```json
{
  "success": true,
  "data": [
    {
      "id": 2,
      "timestamp": "2025-06-03T13:46:47.700Z",
      "station_id": "weather_station_02",
      "temperature": 18.3,
      "humidity": 78.5,
      "pressure": 1008.12,
      "wind_speed": 12.2,
      "wind_direction": 270,
      "rainfall": 2.5,
      "solar_radiation": null,
      "uv_index": null,
      "battery_voltage": null,
      "signal_strength": null,
      "raw_data": "sensor_data_v2.1_checksum_ok",
      "created_at": "2025-06-03 13:46:47"
    }
  ],
  "count": 1,
  "message": "Retrieved 1 weather records"
}
```

## Error Responses

### Validation Error (400)
```json
{
  "success": false,
  "error": "Invalid weather data format",
  "details": [
    {
      "code": "invalid_type",
      "expected": "number",
      "received": "string",
      "path": ["temperature"],
      "message": "Expected number, received string"
    }
  ]
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": "Failed to store weather data",
  "message": "Database connection error"
}
```

## Database Schema

The weather data is stored in SQLite with the following schema:

```sql
CREATE TABLE weather_data (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
  station_id TEXT,
  temperature REAL,
  humidity REAL,
  pressure REAL,
  wind_speed REAL,
  wind_direction REAL,
  rainfall REAL,
  solar_radiation REAL,
  uv_index REAL,
  battery_voltage REAL,
  signal_strength REAL,
  raw_data TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Testing

You can test the API using curl:

```bash
# Store weather data
curl -X POST http://localhost:3000/api/data/weather \
  -H "Content-Type: application/json" \
  -d '{
    "station_id": "test_station",
    "temperature": 25.0,
    "humidity": 60.0,
    "pressure": 1015.0
  }'

# Retrieve weather data
curl -X GET "http://localhost:3000/api/data/weather?limit=5"
```
