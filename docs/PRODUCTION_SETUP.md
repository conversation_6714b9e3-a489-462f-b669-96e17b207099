# Production Setup Guide

This guide explains how to deploy the weather API in production with proper database handling.

## Database Persistence

The weather data is stored in `./data/weather.db` relative to the project root. This ensures:

- **Data persistence**: Database survives deployments and restarts
- **Easy backups**: Simply backup the `data` directory
- **Version control**: Database files are excluded from git via `.gitignore`

## Production Deployment Steps

### 1. Prepare the Data Directory

```bash
# Create data directory if it doesn't exist
mkdir -p data

# Set appropriate permissions (if needed)
chmod 755 data
```

### 2. Environment Setup

The application uses standard SQLite3 and doesn't require additional environment variables for the database. However, ensure Node.js has the necessary permissions to:

- Read/write to the `data` directory
- Create and modify SQLite database files

### 3. Build and Start

```bash
# Install dependencies
npm install

# Build the application
npm run build

# Start in production mode
npm start
```

### 4. Database Migration (Existing Data)

If you're upgrading from a previous version and have existing data, the database schema will be automatically updated when the application starts:

- New tables are created with `CREATE TABLE IF NOT EXISTS`
- New indexes are created with `CREATE INDEX IF NOT EXISTS`
- Existing data remains intact

### 5. Backup Strategy

#### Daily Backup Script

```bash
#!/bin/bash
# backup-weather-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/path/to/backups"
PROJECT_DIR="/path/to/your/project"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Copy database files
cp "$PROJECT_DIR/data/weather.db" "$BACKUP_DIR/weather_$DATE.db"

# Optional: Compress older backups
find "$BACKUP_DIR" -name "weather_*.db" -mtime +7 -exec gzip {} \;

# Optional: Remove backups older than 30 days
find "$BACKUP_DIR" -name "weather_*.db.gz" -mtime +30 -delete

echo "Backup completed: weather_$DATE.db"
```

#### Cron Job Setup

```bash
# Add to crontab (crontab -e)
# Daily backup at 2 AM
0 2 * * * /path/to/backup-weather-db.sh
```

## Docker Deployment

### Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy application code
COPY . .

# Build the application
RUN npm run build

# Create data directory
RUN mkdir -p data

# Expose port
EXPOSE 3000

# Start the application
CMD ["npm", "start"]
```

### Docker Compose

```yaml
version: '3.8'
services:
  weather-api:
    build: .
    ports:
      - "3000:3000"
    volumes:
      - ./data:/app/data
    restart: unless-stopped
    environment:
      - NODE_ENV=production
```

## Monitoring and Maintenance

### Health Check Endpoint

You can monitor the API health by checking:

```bash
curl http://localhost:3000/api/data/weather?limit=1
```

### Database Maintenance

SQLite databases benefit from occasional maintenance:

```bash
# Connect to database
sqlite3 data/weather.db

# Run maintenance commands
VACUUM;
ANALYZE;
PRAGMA integrity_check;
.quit
```

### Log Monitoring

Monitor application logs for:
- Database connection errors
- Disk space warnings
- API request errors

## Security Considerations

### File Permissions

```bash
# Secure the data directory
chmod 750 data
chmod 640 data/weather.db*
```

### Firewall Rules

Ensure only necessary ports are open:
- Port 3000 (or your configured port) for API access
- Restrict access to trusted IP ranges if possible

### API Rate Limiting

Consider implementing rate limiting for the API endpoints to prevent abuse.

## Troubleshooting

### Database Locked Error

If you encounter "database is locked" errors:

1. Check for long-running transactions
2. Ensure proper connection cleanup
3. Consider implementing connection pooling for high-traffic scenarios

### Disk Space Issues

Monitor disk space in the `data` directory:

```bash
# Check disk usage
du -sh data/
df -h .
```

### Performance Optimization

For high-volume deployments:

1. **Indexes**: The application automatically creates indexes on `timestamp` and location fields
2. **WAL Mode**: Enabled by default for better concurrent access
3. **Connection Pooling**: Consider implementing if needed for very high traffic

## Scaling Considerations

For very high-volume scenarios, consider:

1. **Database Sharding**: Split data by time periods or geographic regions
2. **Read Replicas**: Use SQLite replication tools for read-heavy workloads
3. **Migration to PostgreSQL**: For enterprise-scale deployments

## Support

For issues or questions:
1. Check the application logs
2. Verify database file permissions
3. Ensure adequate disk space
4. Review the API documentation in `docs/WEATHER_API.md`
