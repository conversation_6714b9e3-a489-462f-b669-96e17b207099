#!/usr/bin/env node

/**
 * Simple test script for the Weather API
 * Run with: node scripts/test-weather-api.js
 */

const BASE_URL = 'http://localhost:3000/api/data/weather';

async function testWeatherAPI() {
  console.log('🌤️  Testing Weather API...\n');

  try {
    // Test 1: Store weather data
    console.log('📤 Test 1: Storing weather data...');
    const weatherData = {
      station_id: 'test_station_' + Date.now(),
      temperature: 23.5,
      humidity: 68.2,
      pressure: 1012.5,
      wind_speed: 7.2,
      wind_direction: 225,
      rainfall: 0.5,
      solar_radiation: 750.0,
      uv_index: 5.8,
      battery_voltage: 12.6,
      signal_strength: -72,
      raw_data: 'test_data_checksum_valid'
    };

    const postResponse = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(weatherData),
    });

    const postResult = await postResponse.json();
    
    if (postResult.success) {
      console.log('✅ Weather data stored successfully');
      console.log(`   Record ID: ${postResult.data.id}`);
      console.log(`   Station: ${postResult.data.station_id}`);
      console.log(`   Temperature: ${postResult.data.temperature}°C\n`);
    } else {
      console.log('❌ Failed to store weather data');
      console.log('   Error:', postResult.error);
      return;
    }

    // Test 2: Retrieve all weather data
    console.log('📥 Test 2: Retrieving all weather data...');
    const getAllResponse = await fetch(`${BASE_URL}?limit=10`);
    const getAllResult = await getAllResponse.json();

    if (getAllResult.success) {
      console.log(`✅ Retrieved ${getAllResult.data.length} weather records`);
      console.log(`   Total count: ${getAllResult.count}`);
      console.log(`   Latest record: ${getAllResult.data[0]?.station_id || 'None'}\n`);
    } else {
      console.log('❌ Failed to retrieve weather data');
      console.log('   Error:', getAllResult.error);
    }

    // Test 3: Filter by station ID
    console.log('🔍 Test 3: Filtering by station ID...');
    const filterResponse = await fetch(`${BASE_URL}?station_id=${weatherData.station_id}`);
    const filterResult = await filterResponse.json();

    if (filterResult.success) {
      console.log(`✅ Found ${filterResult.data.length} records for station ${weatherData.station_id}`);
      if (filterResult.data.length > 0) {
        const record = filterResult.data[0];
        console.log(`   Temperature: ${record.temperature}°C`);
        console.log(`   Humidity: ${record.humidity}%`);
        console.log(`   Pressure: ${record.pressure} hPa\n`);
      }
    } else {
      console.log('❌ Failed to filter weather data');
      console.log('   Error:', filterResult.error);
    }

    // Test 4: Test validation with invalid data
    console.log('🚫 Test 4: Testing validation with invalid data...');
    const invalidData = {
      temperature: 'not_a_number',
      humidity: 150, // Invalid: > 100
      pressure: -1000 // Invalid: negative
    };

    const invalidResponse = await fetch(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidData),
    });

    const invalidResult = await invalidResponse.json();
    
    if (!invalidResult.success && invalidResponse.status === 400) {
      console.log('✅ Validation correctly rejected invalid data');
      console.log(`   Error: ${invalidResult.error}\n`);
    } else {
      console.log('❌ Validation should have rejected invalid data');
    }

    console.log('🎉 All tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    console.log('\n💡 Make sure the development server is running:');
    console.log('   npm run dev');
  }
}

// Run the tests
testWeatherAPI();
