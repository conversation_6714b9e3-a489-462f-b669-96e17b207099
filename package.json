{"name": "juli<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.0.19", "@headlessui/react": "^2.1.2", "@heroicons/react": "^2.1.5", "@mdx-js/loader": "^3.0.1", "@mdx-js/react": "^3.0.1", "@next/mdx": "^14.2.5", "@types/better-sqlite3": "^7.6.13", "@types/mdx": "^2.0.13", "ai": "^4.0.38", "better-sqlite3": "^11.10.0", "formidable": "^3.5.4", "gray-matter": "^4.0.3", "ioredis": "^5.4.2", "next": "14.2.5", "react": "^18", "react-dom": "^18", "uuid": "^11.0.5", "zod": "^3.24.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}