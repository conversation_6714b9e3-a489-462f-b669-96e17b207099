import { Agent } from "@/components/AgentDemo/AgentService";
import AgentStore from "@/components/AgentDemo/AgentStore";
import { NextRequest } from "next/server";
import { v4 as uuidv4 } from "uuid";

export type AgentStep = {
  type: "initial" | "info" | "reserach" | "task" | "schedule" | "end";
  text: string;
  id: string;
  parentId?: string;
};

export type AgentGETRequest = {
  sessionId: string;
};

export type AgentPOSTRequest = {
  message: string;
};

export async function GET(req: NextRequest) {
  return Response.error()

  const searchParams = req.nextUrl.searchParams;
  const sessionId = searchParams.get("sessionId");

  if (!sessionId) {
    return Response.error();
  }

  const sessionDb = AgentStore.get(sessionId);
  const data: AgentStep[] = sessionDb ? Array.from(sessionDb.values()) : [];

  return Response.json(data);
}

export async function POST(req: Request) {
  return Response.error()

  const { message } = (await req.json()) as AgentPOSTRequest;
  const sessionId = uuidv4();
  const sessionDb = new Map<string, AgentStep>();
  AgentStore.set(sessionId, sessionDb);

  // trigger agent
  const setStepInSession = (stepId: string, step: AgentStep) => {
    const sessionDb = AgentStore.get(sessionId);
    sessionDb?.set(stepId, step);
  };
  new Agent(message, setStepInSession);

  return Response.json({
    sessionId: sessionId,
  });
}
