import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '@/lib/database';
import { 
  WeatherDataSchema, 
  WeatherQuerySchema, 
  WeatherData, 
  WeatherRecord,
  WeatherResponse,
  WeatherCreateResponse 
} from '@/lib/schemas/weather';

// POST - Store weather data from weather station
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate incoming data
    const validationResult = WeatherDataSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid weather data format',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const weatherData: WeatherData = validationResult.data;
    
    // Get database connection
    const db = getDatabase();
    
    // Prepare insert statement
    const insertStmt = db.prepare(`
      INSERT INTO weather_data (
        station_id, timestamp, temperature, humidity, pressure,
        wind_speed, wind_direction, rainfall, solar_radiation,
        uv_index, battery_voltage, signal_strength, raw_data
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    // Execute insert
    const result = insertStmt.run(
      weatherData.station_id || null,
      weatherData.timestamp || new Date().toISOString(),
      weatherData.temperature || null,
      weatherData.humidity || null,
      weatherData.pressure || null,
      weatherData.wind_speed || null,
      weatherData.wind_direction || null,
      weatherData.rainfall || null,
      weatherData.solar_radiation || null,
      weatherData.uv_index || null,
      weatherData.battery_voltage || null,
      weatherData.signal_strength || null,
      weatherData.raw_data || null
    );
    
    // Fetch the created record
    const selectStmt = db.prepare('SELECT * FROM weather_data WHERE id = ?');
    const createdRecord = selectStmt.get(result.lastInsertRowid) as WeatherRecord;
    
    const response: WeatherCreateResponse = {
      success: true,
      data: createdRecord,
      message: 'Weather data stored successfully'
    };
    
    return NextResponse.json(response, { status: 201 });
    
  } catch (error) {
    console.error('Error storing weather data:', error);
    
    const response: WeatherCreateResponse = {
      success: false,
      error: 'Failed to store weather data',
      message: error instanceof Error ? error.message : 'Unknown error'
    };
    
    return NextResponse.json(response, { status: 500 });
  }
}

// GET - Retrieve weather data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse query parameters
    const queryParams = {
      station_id: searchParams.get('station_id'),
      start_date: searchParams.get('start_date'),
      end_date: searchParams.get('end_date'),
      limit: searchParams.get('limit') || '100',
      offset: searchParams.get('offset') || '0',
    };
    
    // Validate query parameters
    const validationResult = WeatherQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid query parameters',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const query = validationResult.data;
    
    // Get database connection
    const db = getDatabase();
    
    // Build dynamic query
    let sql = 'SELECT * FROM weather_data WHERE 1=1';
    const params: any[] = [];
    
    if (query.station_id) {
      sql += ' AND station_id = ?';
      params.push(query.station_id);
    }
    
    if (query.start_date) {
      sql += ' AND timestamp >= ?';
      params.push(query.start_date);
    }
    
    if (query.end_date) {
      sql += ' AND timestamp <= ?';
      params.push(query.end_date);
    }
    
    sql += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?';
    params.push(query.limit, query.offset);
    
    // Execute query
    const stmt = db.prepare(sql);
    const records = stmt.all(...params) as WeatherRecord[];
    
    // Get total count for pagination
    let countSql = 'SELECT COUNT(*) as count FROM weather_data WHERE 1=1';
    const countParams: any[] = [];
    
    if (query.station_id) {
      countSql += ' AND station_id = ?';
      countParams.push(query.station_id);
    }
    
    if (query.start_date) {
      countSql += ' AND timestamp >= ?';
      countParams.push(query.start_date);
    }
    
    if (query.end_date) {
      countSql += ' AND timestamp <= ?';
      countParams.push(query.end_date);
    }
    
    const countStmt = db.prepare(countSql);
    const countResult = countStmt.get(...countParams) as { count: number };
    
    const response: WeatherResponse = {
      success: true,
      data: records,
      count: countResult.count,
      message: `Retrieved ${records.length} weather records`
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('Error retrieving weather data:', error);
    
    const response: WeatherResponse = {
      success: false,
      error: 'Failed to retrieve weather data',
      message: error instanceof Error ? error.message : 'Unknown error'
    };
    
    return NextResponse.json(response, { status: 500 });
  }
}
