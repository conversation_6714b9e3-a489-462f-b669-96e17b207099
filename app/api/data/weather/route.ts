import { NextRequest, NextResponse } from 'next/server';
import { getDatabase, runQuery, getQuery, allQuery } from '@/lib/database';
import {
  WeatherDataSchema,
  WeatherQuerySchema,
  WeatherData,
  WeatherRecord,
  WeatherResponse,
  WeatherCreateResponse
} from '@/lib/schemas/weather';

// POST - Store weather data from weather station
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate incoming data
    const validationResult = WeatherDataSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid weather data format',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const weatherData: WeatherData = validationResult.data;

    // Get database connection
    const db = await getDatabase();

    // Insert weather data
    const insertSql = `
      INSERT INTO weather_data (
        timestamp, station_lat, station_long, temperature,
        wind_speed_meter_per_seconds, is_raining, sun_lux, sun_twilight
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;

    // Execute insert
    const result = await runQuery(db, insertSql, [
      weatherData.timestamp,
      weatherData.station_lat,
      weatherData.station_long,
      weatherData.temperature,
      weatherData.wind_speed_meter_per_seconds,
      weatherData.is_raining ? 1 : 0, // Convert boolean to integer
      weatherData.sun_lux,
      weatherData.sun_twilight ? 1 : 0 // Convert boolean to integer
    ]);

    // Fetch the created record
    const createdRecord = await getQuery(db, 'SELECT * FROM weather_data WHERE id = ?', [result.lastID]) as WeatherRecord;

    // Convert integer fields back to boolean for response
    if (createdRecord) {
      createdRecord.is_raining = Boolean(createdRecord.is_raining);
      createdRecord.sun_twilight = Boolean(createdRecord.sun_twilight);
    }

    const response: WeatherCreateResponse = {
      success: true,
      data: createdRecord,
      message: 'Weather data stored successfully'
    };

    return NextResponse.json(response, { status: 201 });

  } catch (error) {
    console.error('Error storing weather data:', error);

    const response: WeatherCreateResponse = {
      success: false,
      error: 'Failed to store weather data',
      message: error instanceof Error ? error.message : 'Unknown error'
    };

    return NextResponse.json(response, { status: 500 });
  }
}

// GET - Retrieve weather data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const queryParams = {
      station_lat: searchParams.get('station_lat'),
      station_long: searchParams.get('station_long'),
      start_date: searchParams.get('start_date'),
      end_date: searchParams.get('end_date'),
      limit: searchParams.get('limit') || '100',
      offset: searchParams.get('offset') || '0',
    };

    // Validate query parameters
    const validationResult = WeatherQuerySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json({
        success: false,
        error: 'Invalid query parameters',
        details: validationResult.error.errors
      }, { status: 400 });
    }

    const query = validationResult.data;

    // Get database connection
    const db = await getDatabase();

    // Build dynamic query
    let sql = 'SELECT * FROM weather_data WHERE 1=1';
    const params: any[] = [];

    if (query.station_lat !== null && query.station_lat !== undefined) {
      sql += ' AND station_lat = ?';
      params.push(query.station_lat);
    }

    if (query.station_long !== null && query.station_long !== undefined) {
      sql += ' AND station_long = ?';
      params.push(query.station_long);
    }

    if (query.start_date) {
      sql += ' AND timestamp >= ?';
      params.push(query.start_date);
    }

    if (query.end_date) {
      sql += ' AND timestamp <= ?';
      params.push(query.end_date);
    }

    sql += ' ORDER BY timestamp DESC LIMIT ? OFFSET ?';
    params.push(query.limit, query.offset);

    // Execute query
    const records = await allQuery(db, sql, params) as WeatherRecord[];

    // Convert integer fields back to boolean for response
    records.forEach(record => {
      record.is_raining = Boolean(record.is_raining);
      record.sun_twilight = Boolean(record.sun_twilight);
    });

    // Get total count for pagination
    let countSql = 'SELECT COUNT(*) as count FROM weather_data WHERE 1=1';
    const countParams: any[] = [];

    if (query.station_lat !== null && query.station_lat !== undefined) {
      countSql += ' AND station_lat = ?';
      countParams.push(query.station_lat);
    }

    if (query.station_long !== null && query.station_long !== undefined) {
      countSql += ' AND station_long = ?';
      countParams.push(query.station_long);
    }

    if (query.start_date) {
      countSql += ' AND timestamp >= ?';
      countParams.push(query.start_date);
    }

    if (query.end_date) {
      countSql += ' AND timestamp <= ?';
      countParams.push(query.end_date);
    }

    const countResult = await getQuery(db, countSql, countParams) as { count: number };

    const response: WeatherResponse = {
      success: true,
      data: records,
      count: countResult.count,
      message: `Retrieved ${records.length} weather records`
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error retrieving weather data:', error);

    const response: WeatherResponse = {
      success: false,
      error: 'Failed to retrieve weather data',
      message: error instanceof Error ? error.message : 'Unknown error'
    };

    return NextResponse.json(response, { status: 500 });
  }
}
