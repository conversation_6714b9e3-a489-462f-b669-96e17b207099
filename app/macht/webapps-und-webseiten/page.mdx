export const metadata = {
  title: 'Senior Frontend Developer und Freelancer in NÖ, OÖ und Wien',
  description: '<PERSON> ist ein Senior Frontend Developer aus Niederösterreich, spezialisiert auf die Erstellung von Webseiten, Webshops und Webapps auf hohem Niveau. Als Freelancer bietet er Beratung und Unterstützung für Teams an und setzt komplette Webprojekte für Unternehmen um.',
}

import Image from 'next/image'

# Senior Frontend Developer und Freelancer in NÖ, OÖ und Wien

<Image
  src="/senior-frontend-developer-julian-handl.jpg"
  alt="Senior Frontend Developer Julian Handl"
  width={800}
  height={400}
/>

Mein Name ist Julian <PERSON> und ich bin Senior Frontend Developer aus Niederösterreich. Das heißt: Ich erstelle Webseiten, Webshops und Webapps auf hohem Niveau. Als Freelancer können sie mich als Berater oder Unterstützung für ihr Team buchen. Natürlich setze ich auch ganze Webprojekte für ihr Unternehmen um.

## Inhaltsverzeichnis

*   [Meine Leistungen: Ich bringe Ihr Frontend auf das nächste Level](#h-meine-leistungen-ich-bringe-ihr-frontend-auf-das-n-chste-level)
    *   [Umsetzung ganzer Webprojekte](#h-umsetzung-ganzer-webprojekte)
    *   [Consulting Leistung](#h-consulting-leistung)
*   [Der Job des Frontend Developers](#h-der-job-des-frontend-developers)
*   [Meine Kernkompetenzen als Frontend Developer](#h-meine-kernkompetenzen-als-frontend-developer)
    *   [React](#h-react)
    *   [Typescript](#h-typescript)
    *   [CSS / LESS / SASS](#h-css-less-sass)
    *   [Next.js](#h-next-js)
    *   [Docker](#h-docker)
    *   [Pipelines](#h-pipelines)
    *   [DevOps](#h-devops)

## Meine Leistungen: Ich bringe Ihr Frontend auf das nächste Level

Technologien ändern sich. Computer werden schneller. Kunden werden anspruchsvoller. Genau hier setze ich an.

Egal ob sie ein Produkt, eine Dienstleistung oder sich selbst online vermarkten. Ihre Werbung ist immer nur so gut wie sie aussieht. Da es für den ersten Eindruck keine zweite Chance gibt, ist ein gelungenes Frontend (Webseite, Webapp, …) unerlässlich für jeden. Wenn ein Kunde ihre Webseite besucht können sie sich nicht herausreden. Entschuldigungen wie »das wird nächste Woche noch verbessert« zählen nichts, denn entweder es funktioniert und fühlt sich gut an, oder eben nicht.

### Umsetzung ganzer Webprojekte

Von Design bis Programmierung und Marketing setze ich mit meinem Partnernetzwerk Projekte aller Art um. Fordern Sie mich ruhig mit etwas schwierigem heraus!

Mein Teil der Webprojekte umfasst die Konzeption und technische Umsetzung. Konkret heißt das, dass ich vorab gemeinsam mit ihnen an den Anforderungen arbeite und diese zu Papier bringe. Danach folgt die konkrete Planung und Recherche zur perfekten Umsetzung. [Hier entstehen auch SEO Konzept für ideales Online-Marketing](http://julianhandl.at/seo-spezialist-niederosterreich-wien-und-oberosterreich/). Schließlich präsentiere ich ihnen den vorgeschlagenen Plan und setzte das Projekt nach ihrer Absegnung um.

Ein großes Anliegen ist mir die reibungslose Kommunikation. Einerseits wollen sie nicht die Katze im Sack bekommen, andererseits auch nicht mit technischen Details überfordert werden. Sie erhalten zu Projektbeginn einen fixen Kommunikationspartner und werden in regelmäßigen Abständen auf den neuesten Stand gebracht. In den meisten Projekten erhalten sie sogar eine Online-Preview, auf der sie den Fortschritt live miterleben können.

**[Kontaktieren sie mich für ein unverbindliches Angebot.](http://julianhandl.at/kontakt-impressum/)**

### Consulting Leistung

Oft braucht es nur einen Schubs in die richtige Richtung. Viele Unternehmen haben bereits ein Team, dass vor einer großen Herausforderung steht. Oft hat noch keiner der Entwickler ein System dieser Art oder Größe umgesetzt.

In diesem Fall ist es sinnvoll Hilfe von Außen zu holen. Oft geht es nur um ein Konzept, das Prüfen der Machbarkeit oder eine zweite Meinung.

Als Consultant im Frontend Bereich helfe ich ihnen und ihrem Team die richtige Richtung einzuschlagen.

**[Kontaktieren sie mich für meine Stundensätze und Verfügbarkeit.](http://julianhandl.at/kontakt-impressum/)**

## Der Job des Frontend Developers

Der Umfang des Frontends ist weit über HTML / CSS / JS hinausgewachsen. Interfaces sind interaktiv geworden und beinhalten sogar Businesslogik. Viel des ehemaligen Server-Codes ist ins Frontend gewandert, um direkt bei Kunden zu agieren.

Die Palette der Frontend-Technologien ist endlos. Ich konzentriere mich auf performante, sichere und stabile Technologien um Webapp und Webseiten umzusetzen.

Der moderne Frontend Developer beherrscht auch Technologien, die über das Frontend hinaus gehen. Verwaltungsskills, Hosting, Virtualisierung und vieles mehr. Auch zwischenmenschliche Skills wie Team-Lead Kompetenzen sind gefragt.

## Meine Kernkompetenzen als Frontend Developer

### [React](http://julianhandl.at/react-freelancer/)

React ist de facto das populärste Frontend Framework und das nicht ohne Grund. Schnell, gute Community und von einem Tech-Giganten gestützt.

Ich selbst arbeite seit 2016 mit React und habe es lieben gelernt. Egal ob Statemanagement, Animationen oder Hooks. Im React Ökosystem habe ich alles gesehen, verwendet und geliebt.

### [Angular](http://julianhandl.at/angular-freelancer/)

Neben React baut die Basis des modernen Internets auf Angular. Auch damit habe ich an großen Webapps im Fintech Bereich gearbeitet.

### Typescript

Das moderne Web kommt ohne TypeScript nicht mehr aus. Typsicheres JavaScript ermöglicht Webapps in einer ganz neuen Größenordnung.

Seit 2016 arbeite ich fast ausschließlich mit TypeScript wenn um Javascript geht.

### CSS / LESS / SASS

CSS gehört zu den Basics einen jeden Frontend Developers.

Über 10 Jahre Erfahrung zeichnen meine CSS Skills. Angefangen mit Table-Layouts bis Flexbox und Grid. Von CSS über Precompiler zu LESS und SASS. Aktuell arbeite ich meist mit SASS und CSS-Modules.

### Next.js

Der Baukasten für React Frontends schlechthin.

2018 habe ich mein eigenes Framework für serverseitig gerenderte Webapps mit React gebaut. 2020 bin ich auf Next.js umgestiegen, weil es von einer großen Firma gestützt wird und die Entwicklung schnell voranging. Aktuell setzte ich fast jede Webseite und so manche Webapp mit Next.js um.

### Docker

Docker erleichtert fast alles. Entwicklungsumgebungen, Deployments und Pipelines. Laufzeitumgebungen sind vorhersehbarer und Deployments sind meist nur einzeilige Konsolenbefehle.

Seit 2017 setzte ich in fast jedem Projekt Docker ein.

### Pipelines

Einfach ruhig schlafen können. Pipelines testen den neuesten Projektstand vollautomatisch und veröffentlichen diesen, wenn alles glattläuft.

Seit 2017 gibt es bei jedem meiner Projekte die einen Buildprozess brauchen auch eine Pipeline, die diesen durchführt. Entweder auf Azure oder Gitlab werden meine Projekte vollautomatisch getestet, mit Docker gebaut und in die Registry gepusht und auf dem Server deployt.

### DevOps

Auch DevOps gehört zu einem vollständigen Frontend Developer. Projekte müssen auf den Server und veröffentlicht werden.

Seit jeher verwende ich auf meinen Servern meist ein Docker-Setup. Nginx als Webserver und Letsencrypt für sichere Verbindungen. Wenn das Standard-Setup noch nicht schnell genug ist, wird auch noch ein Cache wie Varnish konfiguriert.