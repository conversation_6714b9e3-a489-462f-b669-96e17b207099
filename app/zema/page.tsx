import Link from "next/link";
import ChatContainer from "./components/ChatContainer";

export default function ZemaPage() {
  return (
    <div className="h-screen overflow-hidden bg-white">
      {/* Minimal header with just logo */}
      <div className="absolute top-0 left-0 z-10 p-4">
        <Link href="/" className="block">
          <img
            src="/julianhandl.svg"
            alt="Julian Handl Logo"
            className="h-12 hover:opacity-80 transition-opacity"
          />
        </Link>
      </div>
      <main className="h-screen">
        <ChatContainer />
      </main>
    </div>
  );
}

export const metadata = {
  title: "Zema Chat - AI Assistant",
  description: "<PERSON><PERSON> with <PERSON><PERSON>, your AI assistant",
};
