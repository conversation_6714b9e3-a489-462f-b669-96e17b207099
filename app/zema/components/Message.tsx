import { Message as MessageType } from "../types";
import { MESSAGE_TYPES } from "../constants";

interface MessageProps {
  message: MessageType;
}

export default function Message({ message }: MessageProps) {
  const isUser = message.type === MESSAGE_TYPES.USER;

  return (
    <div
      className={`flex w-full ${isUser ? "justify-end" : "justify-start"} mb-4`}
    >
      <div
        className={`flex max-w-[80%] ${
          isUser ? "flex-row-reverse" : "flex-row"
        } items-start space-x-3`}
      >
        {/* Avatar */}
        {isUser ? (
          <div
            className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium bg-blue-500 text-white ml-3"
          >U</div>
        ) : null}

        {/* Message Content */}
        <div
          className={`
          px-4 py-3 rounded-2xl max-w-full break-words
          ${isUser ? "bg-blue-500 text-white rounded-br-md" : "text-gray-900"}
          ${message.isStreaming ? "animate-pulse" : ""}
        `}
        >
          <div className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
            {message.isStreaming && (
              <span className="inline-block w-2 h-4 bg-current ml-1 animate-pulse">
                |
              </span>
            )}
          </div>
          <div
            className={`
            text-xs mt-2 opacity-70
            ${isUser ? "text-blue-100" : "text-gray-500"}
          `}
          >
            {message.timestamp.toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
