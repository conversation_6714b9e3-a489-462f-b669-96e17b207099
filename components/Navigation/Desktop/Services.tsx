import {
  ArrowTrendingUpIcon,
  BookmarkSquareIcon,
  CalendarDaysIcon,
  CodeBracketIcon,
  LifebuoyIcon,
} from "@heroicons/react/24/outline";
import { Popover, PopoverButton, PopoverPanel } from "@headlessui/react";

import { ChevronDownIcon } from "@heroicons/react/20/solid";

const resources = [
  {
    name: "Webapps",
    description: "Digitale Werkzeuge für Ihr Unternehmen",
    href: "/macht/webapps-und-webseiten",
    icon: CodeBracketIcon,
  },
  {
    name: "AI-Tools",
    description: "Integration von AI in Firmenstrukturen",
    href: "/macht/ai-tools",
    icon: ArrowTrendingUpIcon,
  },
  /*
  {
    name: "SEO",
    description: "Ihre Sichtbarkeit im Web verbessern",
    href: "/macht/seo",
    icon: ArrowTrendingUpIcon,
  },
  */
];
const recentPosts = [
  {
    id: 1,
    title: "Boost your conversion rate",
    href: "#",
    date: "Mar 5, 2023",
    datetime: "2023-03-05",
  },
  {
    id: 2,
    title:
      "How to use search engine optimization to drive traffic to your site",
    href: "#",
    date: "Feb 25, 2023",
    datetime: "2023-02-25",
  },
  {
    id: 3,
    title: "Improve your customer experience",
    href: "#",
    date: "Feb 21, 2023",
    datetime: "2023-02-21",
  },
];

export const ServicesNavigation: React.FC = () => {
  return (
    <Popover className="relative">
      <PopoverButton className="outline-none inline-flex items-center gap-x-1 text-md font-semibold leading-6 focus:text-pink-600">
        <span>Ich mache</span>
        <ChevronDownIcon aria-hidden="true" className="h-5 w-5" />
      </PopoverButton>

      <PopoverPanel
        transition
        className="absolute left-1/2 z-10 mt-5 flex w-screen max-w-max -translate-x-1/2 px-4 transition data-[closed]:translate-y-1 data-[closed]:opacity-0 data-[enter]:duration-200 data-[leave]:duration-150 data-[enter]:ease-out data-[leave]:ease-in"
      >
        <div className="w-screen max-w-md flex-auto overflow-hidden rounded-3xl bg-white text-sm leading-6 shadow-lg ring-1 ring-gray-900/5">
          <div className="p-4">
            {resources.map((item) => (
              <div
                key={item.name}
                className="group relative flex gap-x-6 rounded-lg p-4 hover:bg-gray-50"
              >
                <div className="mt-1 flex h-11 w-11 flex-none items-center justify-center rounded-lg bg-gray-50 group-hover:bg-gray-100">
                  <item.icon
                    aria-hidden="true"
                    className="h-6 w-6 text-gray-600 group-hover:text-indigo-600"
                  />
                </div>
                <div>
                  <a href={item.href} className="font-semibold text-gray-900">
                    {item.name}
                    <span className="absolute inset-0" />
                  </a>
                  <p className="mt-1 text-gray-600">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
          {/* 
          <div className="bg-gray-50 p-8">
            <div className="flex justify-between">
              <h3 className="text-sm font-semibold leading-6 text-gray-500">
                Blog posts 
              </h3>
              <a
                href="#"
                className="text-sm font-semibold leading-6 text-indigo-600"
              >
                See all <span aria-hidden="true">&rarr;</span>
              </a>
            </div>
            <ul role="list" className="mt-6 space-y-6">
              {recentPosts.map((post) => (
                <li key={post.id} className="relative">
                  <time
                    dateTime={post.datetime}
                    className="block text-xs leading-6 text-gray-600"
                  >
                    {post.date}
                  </time>
                  <a
                    href={post.href}
                    className="block truncate text-sm font-semibold leading-6 text-gray-900"
                  >
                    {post.title}
                    <span className="absolute inset-0" />
                  </a>
                </li>
              ))}
            </ul>
          </div>
          */}
        </div>
      </PopoverPanel>
    </Popover>
  );
};
