"use client";

import Link from "next/link";
import { useState } from "react";

export function MobileNavigation() {
  const [isOpen, setIsOpen] = useState(false);

  const toggleNav = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div>
      {/* Hamburger Icon */}
      <button
        aria-label="Open Menu"
        onClick={toggleNav}
        className="p-2 text-gray-800 focus:outline-none"
      >
        <svg
          className="w-8 h-8"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M4 6h16M4 12h16M4 18h16"
          />
        </svg>
      </button>

      {/* Fullscreen Navigation Overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-pink-600 text-white">
          {/* Close Button (X) */}
          <button
            aria-label="Close Menu"
            onClick={toggleNav}
            className="absolute top-8 right-6 p-2 focus:outline-none"
          >
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          {/* Navigation Content */}
          <nav className="flex flex-col space-y-4 w-full text-2xl mt-10">
            {/* Card 1 mit gestaffelter Animation */}
            <div
              className="bg-pink-700 border border-white rounded-lg p-6 mx-4 opacity-0 transform translate-y-2"
              style={{
                animation: "slideDown 0.3s ease-out forwards",
                animationDelay: "0.1s",
              }}
            >
              <h2 className="text-3xl font-bold mb-4">Ich mache</h2>
              <ul className="space-y-3">
                <li>
                  <Link
                    href="/macht/webapps-und-webseiten"
                    className="hover:underline"
                  >
                    Webapps
                  </Link>
                </li>
                <li>
                  <Link href="/macht/ai-tools" className="hover:underline">
                    AI-Tools
                  </Link>
                </li>
                {/*
                <li>
                  <Link href="/macht/seo" className="hover:underline">
                    SEO
                  </Link>
                </li>
                */}
              </ul>
            </div>

            {/* Card 2 mit gestaffelter Animation */}
            <div
              className="bg-pink-700 border border-white rounded-lg p-6 mx-4 opacity-0 transform translate-y-2"
              style={{
                animation: "slideDown 0.3s ease-out forwards",
                animationDelay: "0.2s",
              }}
            >
              <h2 className="text-3xl font-bold mb-4">Ich bin</h2>
              <ul className="space-y-3">
                <li>
                  <Link
                    href="/ist-ein/software-architekt"
                    className="hover:underline"
                  >
                    Software-Architekt
                  </Link>
                </li>
                <li>
                  <Link href="/ist-ein/mensch" className="hover:underline">
                    eine normale Person
                  </Link>
                </li>
              </ul>
            </div>
          </nav>

          {/* Kontakt Button */}
          <div className="absolute bottom-10">
            <a
              href="/kontakt"
              className="bg-white text-pink-600 px-6 py-3 rounded-full text-lg font-semibold hover:bg-gray-100"
            >
              Kontakt
            </a>
          </div>
        </div>
      )}
    </div>
  );
}
