import Footer from "@/components/Footer/Footer";
import Link from "next/link";
import { Navigation } from "@/components/Navigation/Navigation";

interface WebsiteLayoutProps {
  children: React.ReactNode;
}

export default function WebsiteLayout({ children }: WebsiteLayoutProps) {
  return (
    <div className="flex flex-col min-h-screen justify-between">
      <header>
        <nav
          aria-label="Global"
          className="mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8"
        >
          <div className="flex lg:flex-1">
            <a href="/" className="-m-1.5 p-1.5">
              <img
                src="/julianhandl.svg"
                alt="Julian Handl Logo"
                className="h-16"
              />
            </a>
          </div>
          <Navigation />
          <div className="hidden lg:flex lg:flex-1 lg:justify-end">
            <Link
              href="/kontakt-impressum"
              className="rounded-md text-pink-600 border border-solid border-pink-600 px-3.5 py-2.5 text-sm font-semibold"
            >
              Kontakt
            </Link>
          </div>
        </nav>
      </header>
      {children}
      <Footer />
    </div>
  );
}
