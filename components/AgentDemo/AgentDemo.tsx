"use client";

import {
  AgentGETRequest,
  AgentPOSTRequest,
  AgentStep,
} from "@/app/api/ai/agent/route";

import AgentInput from "@/components/AgentDemo/AgentInput";
import { AgentSteps } from "@/components/AgentDemo/AgentSteps";
import { useState } from "react";

export const AgentDemo: React.FC = () => {
  const [message, setMessage] = useState("");
  const [agentSteps, setAgentSteps] = useState<AgentStep[]>([]);

  const submit = async (message: string) => {
    setMessage(message);

    const res = await fetch("/api/ai/agent", {
      method: "POST",
      body: JSON.stringify({ message } as AgentPOSTRequest),
    });
    const { sessionId } = (await res.json()) as { sessionId: string };

    const pollAgent = async () => {
      const res = await fetch(`/api/ai/agent?sessionId=${sessionId}`);
      const steps = (await res.json()) as AgentStep[];
      setAgentSteps(steps);
      if (steps[steps.length - 1].type !== "end") {
        setTimeout(pollAgent, 1000);
      }
    };
    setTimeout(pollAgent, 1000);
    console.log("sessionId", sessionId);
  };

  return (
    <div>
      <AgentInput onSubmit={submit} />
      {agentSteps.length ? <AgentSteps steps={agentSteps} /> : null}
    </div>
  );
};
