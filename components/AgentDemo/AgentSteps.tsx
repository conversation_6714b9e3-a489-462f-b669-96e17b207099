import { AgentStep } from "@/app/api/ai/agent/route";
import { CheckCircleIcon } from "@heroicons/react/24/outline";

function classNames(...classes) {
  return classes.filter(Boolean).join(" ");
}

export const AgentSteps: React.FC<{ steps: AgentStep[] }> = ({ steps }) => {
  const rootSteps = steps.filter((s) => !s.parentId);

  return (
    <ul role="list" className="space-y-3 !pl-0 min-h-48">
      {rootSteps.map((step, stepIndex) => {
        const childSteps = steps.filter((s) => s.parentId === step.id);
        return (
          <Step
            step={step}
            index={stepIndex}
            key={step.id}
            childSteps={childSteps}
          />
        );
      })}
    </ul>
  );
};

function Step(props: {
  step: AgentStep;
  index: number;
  childSteps: AgentStep[];
}) {
  const { step, index, childSteps } = props;

  let cardContent;

  if (step.type === "info" || step.type === "end") {
    cardContent = (
      <p className="flex-auto !p-0 !m-0 text-xs/5 text-gray-500">{step.text}</p>
    );
  } else {
    cardContent = (
      <div
        className={classNames(
          step.type === "reserach"
            ? "ring-green-600 bg-green-50 !text-green-800"
            : "",
          step.type === "task" ? "!ring-blue-600 bg-blue-50 text-blue-800" : "",
          step.type === "schedule" ? "ring-sky-600 bg-sky-50 text-sky-800" : "",
          "flex-auto rounded-md p-3 ring-1 ring-inset ring-gray-200"
        )}
      >
        <p className="text-sm/6 !m-0">{step.text}</p>
      </div>
    );
  }

  return (
    <li
      key={step.id}
      className="relative flex flex-col gap-x-4 !p-0 animate-fadeInUp opacity-0 !mb-0"
      style={{ animationDelay: `${index * 0.3}s` }}
    >
      {cardContent}
      {childSteps.length > 0 ? (
        <ul className="!p-0 !m-0 !ml-3">
          {childSteps.map((childStep, stepIndex) => {
            return (
              <Step
                step={childStep}
                index={stepIndex}
                key={step.id}
                childSteps={[]}
              />
            );
          })}
        </ul>
      ) : null}
    </li>
  );
}
