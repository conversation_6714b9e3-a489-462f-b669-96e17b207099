import { LanguageModelV1, generateObject, generateText, tool } from "ai";

import { AgentStep } from "@/app/api/ai/agent/route";
import { openai } from "@ai-sdk/openai";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";

const model: LanguageModelV1 = openai("gpt-4o-mini");

type SetStepFunction = (id: string, step: AgentStep) => void;

const baseAssistantSystemMessage = `
  Du bist die Assistenz der Geschäftsführung in einem
  mittelständischen Produktionsbetrieb in Österreich.
  Antworte immer auf deutsch.
`;

export class Agent {
  constructor(
    private originalMessage: string,
    private setStep: SetStepFunction
  ) {
    this.start();
  }

  async start() {
    console.log("start the agent");

    const initialStepId = uuidv4();
    this.setStep(initialStepId, {
      type: "initial",
      text: "Starte den Agent.",
      id: initialStepId,
    });

    const { object } = await generateObject({
      model,
      system: `
        ${baseAssistantSystemMessage}
        Dein Chef gibt dir Aufgaben als Text.
        Nimm nur die erste Aufgabe und entferne die restlichen Aufgabe.
        Klassifiziere um welche Art von Aufgabe es sich handelt.
        Gib mir die Aufgabe der task Property.
      `,
      prompt: this.originalMessage,
      schema: z.object({
        taskType: z.union([
          z.literal("research"),
          z.literal("task"),
          z.literal("schedule"),
        ]),
        task: z.string(),
      }),
    });

    console.log("result", object);

    switch (object.taskType) {
      case "research":
        await new ResearchAgent(object.task, this.setStep).start();
        break;
      case "task":
        await new TaskAgent(object.task, this.setStep).start();
        break;
      case "schedule":
        await new ScheduleAgent(object.task, this.setStep).start();
        break;
      default:
        this.setStep(initialStepId, {
          type: "info",
          text: "Error.",
          id: initialStepId,
        });
        break;
    }

    const endStep = uuidv4();
    this.setStep(endStep, {
      type: "end",
      text: "Ende",
      id: endStep,
    });
  }
}

class ResearchAgent {
  constructor(
    private prompt: string,
    private setStep: SetStepFunction,
    private partentTask?: string
  ) {}

  async start() {
    const { object } = await generateObject({
      model,
      system: `
        ${baseAssistantSystemMessage}
        Dein Chef gibt dir eine Aufgabe zu recherchieren.
        Erstelle eine Liste an Themen die Recherchiert werden sollten.
        Erstelle zu jedem Eintrag eine Beschreibung was du recherchierst aus der Ich Perspektive.
        Die Beschreibung darf nur einen Satz mit maximal 20 Wörter lang sein.
        Maximal 5 Themen.
      `,
      prompt: this.prompt,
      schema: z.object({
        topic: z.array(
          z.object({
            description: z.string(),
          })
        ),
      }),
    });

    const researchTasks = object.topic.map((topic) => ({
      text: topic.description,
      uuid: uuidv4(),
    }));

    researchTasks.forEach((task) => {
      this.setStep(task.uuid, {
        type: "reserach",
        text: task.text,
        id: task.uuid,
      });
    });

    const { object: objectResearchTasks } = await generateObject({
      model,
      system: `
        ${baseAssistantSystemMessage}
        Es wurden Recherchen durchgeführt. Diese werden als JSON übergeben.
        Jede Recherche hat eine Beschreibung was gemacht wurde und eine id.
        Analysiere die Recherchen und erstelle notwendige Aufgaben die daraus resultieren.
        Eine Aufgabe kann nach allen Recherchen erfolgen, oder einer speziellen Recherche zugeordnet sein.
        Wenn eine Aufgabe zu einer speziellen Recherche gehört, setzte die rechercheId.
        Die Beschreibung darf nur einen Satz mit maximal 20 Wörter lang sein.
        Maximal 3 Aufgaben.
      `,
      prompt: JSON.stringify(researchTasks),
      schema: z.object({
        tasks: z.array(
          z.object({
            researchId: z.string().optional().nullable(),
            description: z.string(),
          })
        ),
      }),
    });

    for (const task of objectResearchTasks.tasks) {
      const stepId = uuidv4();
      this.setStep(stepId, {
        type: "task",
        text: task.description!,
        id: stepId,
        parentId: task.researchId ?? undefined,
      });
    }
  }
}

class TaskAgent {
  constructor(
    private prompt: string,
    private setStep: SetStepFunction,
    private partentTask?: string
  ) {}

  async start() {
    const { object } = await generateObject({
      model,
      system: `
        ${baseAssistantSystemMessage}
        Dein Chef gibt dir eine Aufgabe etwas zu erledigen.
        Erstelle eine Liste an folgenden Dingen:
        - Dingen die du machst um die Aufgabe zu erledigen.
        - Dinge die du immer wieder machen musst um die Aufgabe zu verfolgen.
        - Dinge die du zu einem späteren Zeitpunkt  machen musst um die Aufgabe abzuschließen.
        Erstelle zu jedem Eintrag eine Beschreibung was du tust aus der Ich Perspektive.
        Die Beschreibung darf nur einen Satz mit maximal 20 Wörter lang sein.
        Klassifiziere die Aufgaben ob sie einfache Aufgaben (task) sind,
        oder wiederkehrende Aufgaben (schedule) im type Property.
        Maximal 7 Aufgaben.
      `,
      prompt: this.prompt,
      schema: z.object({
        topic: z.array(
          z.object({
            type: z.union([z.literal("task"), z.literal("schedule")]),
            description: z.string(),
          })
        ),
      }),
    });

    for (const topic of object.topic) {
      const stepId = uuidv4();

      if (topic.type === "schedule") {
        this.setStep(stepId, {
          type: "task",
          text: topic.description,
          id: stepId,
          parentId: this.partentTask,
        });
        const prompt = `
          ${baseAssistantSystemMessage}
          Du hast von deinem Chef folgende Aufgabe erhalten:
          ${this.prompt}

          Deine Aufgabe ist die folgende:
          ${topic.description}
        `;
        await new ScheduleAgent(prompt, this.setStep, stepId, 2).start();
      } else {
        this.setStep(stepId, {
          type: "task",
          text: this.prompt,
          id: stepId,
          parentId: this.partentTask,
        });
      }
    }
  }
}

class ScheduleAgent {
  constructor(
    private prompt: string,
    private setStep: SetStepFunction,
    private partentTask?: string,
    private maxSchedules?: number
  ) {}

  async start() {
    const { object } = await generateObject({
      model,
      system: `
        ${baseAssistantSystemMessage}
        Dein Chef gibt dir eine Aufgabe die regelmäßig erledigt werden soll.
        Erstelle eine Liste an Dingen die regelmäßig gemacht werden müssen.
        Du sollst so viele Aufgaben wie möglich übernehmen,
        direkte Kontaktaufnahme wie Person zu Person oder ein Telefonat müssen vom Chef erledigt werden.
        Erstelle zu jedem Eintrag eine Beschreibung was du tust, wann und wie oft es getan werden muss.
        Die Beschreibung darf nur maximal 30 Wörter lang sein.
        Maximal ${this.maxSchedules ?? 5} Aufgaben.
      `,
      prompt: this.prompt,
      schema: z.object({
        topic: z.array(
          z.object({
            description: z.string(),
          })
        ),
      }),
    });

    object.topic.forEach((topic) => {
      const stepId = uuidv4();
      this.setStep(stepId, {
        type: "schedule",
        text: topic.description,
        id: stepId,
        parentId: this.partentTask,
      });
    });
  }
}
