"use client";

import { useState } from "react";

const rechercheBeispiel = `Meine Firma kämpft um nationale Kunden. Wie kann ich mehr Kunden auf Landesebene ansprechen?`;
const aufgabenBeispiel = `Plane und beauftrage die Erweiterung unserer Produktionsstätte für das kommende Jahr.`;
const erinnerungsBeispiel = `Erin<PERSON>e mich regelm<PERSON>ßig daran, Mitarbeitergespräche zu führen.`;

type Props = {
  onSubmit: (message: string) => void;
};

export default function AgentInput(props: Props) {
  const [message, setMessage] = useState("");

  return (
    <form
      action="#"
      className="relative"
      onSubmit={(e) => {
        e.preventDefault();
        props.onSubmit(message);
      }}
    >
      <div className="rounded-lg bg-white outline outline-1 -outline-offset-1 outline-gray-300 focus-within:outline focus-within:outline-2 focus-within:-outline-offset-2 focus-within:outline-indigo-600">
        <div className="block w-full px-3 pt-2.5 text-lg font-medium text-gray-900">
          Schreibe dem AI-Agent
        </div>
        <label htmlFor="description" className="sr-only">
          Description
        </label>
        <textarea
          id="description"
          name="description"
          rows={2}
          placeholder="Ich will dass meine Firma besser wird beim ..."
          className="block w-full resize-none px-3 py-1.5 text-base text-gray-900 placeholder:text-gray-400 focus:outline focus:outline-0 sm:text-sm/6"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
        />

        {/* Spacer element to match the height of the toolbar */}
        <div aria-hidden="true">
          <div className="py-2">
            <div className="h-9" />
          </div>
          <div className="h-px" />
          <div className="py-2">
            <div className="py-px">
              <div className="h-9" />
            </div>
          </div>
        </div>
      </div>

      <div className="absolute inset-x-px bottom-0">
        {/* Actions: These are just examples to demonstrate the concept, replace/wire these up however makes sense for your project. */}
        <div className="flex flex-nowrap justify-end space-x-2 px-2 py-2 sm:px-3">
          <div className="relative">
            <button
              className="relative inline-flex items-center whitespace-nowrap rounded-full bg-gray-50 px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-3"
              onClick={(e) => {
                e.preventDefault();
                setMessage(rechercheBeispiel);
              }}
            >
              Recherche Beispiel
            </button>
          </div>

          <div className="relative">
            <button
              className="relative inline-flex items-center whitespace-nowrap rounded-full bg-gray-50 px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-3"
              onClick={(e) => {
                e.preventDefault();
                setMessage(aufgabenBeispiel);
              }}
            >
              Aufgaben Beispiel
            </button>
          </div>

          <div className="relative">
            <button
              className="relative inline-flex items-center whitespace-nowrap rounded-full bg-gray-50 px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 sm:px-3"
              onClick={(e) => {
                e.preventDefault();
                setMessage(erinnerungsBeispiel);
              }}
            >
              Erinnerungs Beispiel
            </button>
          </div>
        </div>
        <div className="flex items-center justify-between space-x-3 border-t border-gray-200 px-2 py-2 sm:px-3">
          <div className="flex">
            <div className="-my-2 -ml-2 inline-flex items-center rounded-full px-3 py-2 text-left text-gray-400">
              <span className="text-sm italic text-gray-500">
                Sie sind Chef eines mittelständischen Produktionsbetriebs
              </span>
            </div>
          </div>
          <div className="shrink-0">
            <button
              type="submit"
              className="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Senden
            </button>
          </div>
        </div>
      </div>
    </form>
  );
}
