import { z } from 'zod';

// Weather data schema for validation
export const WeatherDataSchema = z.object({
  station_id: z.string().optional(),
  timestamp: z.string().datetime().optional(), // ISO 8601 format
  temperature: z.number().optional(),
  humidity: z.number().min(0).max(100).optional(),
  pressure: z.number().positive().optional(),
  wind_speed: z.number().min(0).optional(),
  wind_direction: z.number().min(0).max(360).optional(),
  rainfall: z.number().min(0).optional(),
  solar_radiation: z.number().min(0).optional(),
  uv_index: z.number().min(0).optional(),
  battery_voltage: z.number().positive().optional(),
  signal_strength: z.number().optional(),
  raw_data: z.string().optional(),
});

// TypeScript type derived from schema
export type WeatherData = z.infer<typeof WeatherDataSchema>;

// Database record type (includes auto-generated fields)
export interface WeatherRecord extends WeatherData {
  id: number;
  created_at: string;
}

// Query parameters for GET requests
export const WeatherQuerySchema = z.object({
  station_id: z.string().optional().nullable(),
  start_date: z.string().datetime().optional().nullable(),
  end_date: z.string().datetime().optional().nullable(),
  limit: z.coerce.number().min(1).max(1000).default(100),
  offset: z.coerce.number().min(0).default(0),
});

export type WeatherQuery = z.infer<typeof WeatherQuerySchema>;

// Response types
export interface WeatherResponse {
  success: boolean;
  data?: WeatherRecord[];
  count?: number;
  message?: string;
  error?: string;
}

export interface WeatherCreateResponse {
  success: boolean;
  data?: WeatherRecord;
  message?: string;
  error?: string;
}
