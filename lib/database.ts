import Database from 'better-sqlite3';
import path from 'path';

// Database file path - stores in project root
const DB_PATH = path.join(process.cwd(), 'weather.db');

// Singleton database instance
let db: Database.Database | null = null;

export function getDatabase(): Database.Database {
  if (!db) {
    db = new Database(DB_PATH);
    
    // Enable WAL mode for better concurrent access
    db.pragma('journal_mode = WAL');
    
    // Initialize tables
    initializeTables();
  }
  
  return db;
}

function initializeTables() {
  if (!db) return;
  
  // Create weather_data table
  const createWeatherTable = `
    CREATE TABLE IF NOT EXISTS weather_data (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
      station_id TEXT,
      temperature REAL,
      humidity REAL,
      pressure REAL,
      wind_speed REAL,
      wind_direction REAL,
      rainfall REAL,
      solar_radiation REAL,
      uv_index REAL,
      battery_voltage REAL,
      signal_strength REAL,
      raw_data TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `;
  
  db.exec(createWeatherTable);
  
  // Create index on timestamp for faster queries
  const createTimestampIndex = `
    CREATE INDEX IF NOT EXISTS idx_weather_timestamp 
    ON weather_data(timestamp)
  `;
  
  db.exec(createTimestampIndex);
  
  // Create index on station_id for multi-station support
  const createStationIndex = `
    CREATE INDEX IF NOT EXISTS idx_weather_station 
    ON weather_data(station_id)
  `;
  
  db.exec(createStationIndex);
}

// Graceful shutdown
export function closeDatabase() {
  if (db) {
    db.close();
    db = null;
  }
}

// Handle process termination
process.on('exit', closeDatabase);
process.on('SIGINT', closeDatabase);
process.on('SIGTERM', closeDatabase);
