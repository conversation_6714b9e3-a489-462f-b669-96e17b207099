import sqlite3 from 'sqlite3';
import path from 'path';
import fs from 'fs';

// Database file path - stores in ./data directory
const DATA_DIR = path.join(process.cwd(), 'data');
const DB_PATH = path.join(DATA_DIR, 'weather.db');

// Singleton database instance
let db: sqlite3.Database | null = null;

// Promise wrapper for sqlite3 operations
function runQuery(db: sqlite3.Database, sql: string, params: any[] = []): Promise<any> {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function(err) {
      if (err) reject(err);
      else resolve({ lastID: this.lastID, changes: this.changes });
    });
  });
}

function getQuery(db: sqlite3.Database, sql: string, params: any[] = []): Promise<any> {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) reject(err);
      else resolve(row);
    });
  });
}

function allQuery(db: sqlite3.Database, sql: string, params: any[] = []): Promise<any[]> {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) reject(err);
      else resolve(rows);
    });
  });
}

export async function getDatabase(): Promise<sqlite3.Database> {
  if (!db) {
    // Ensure data directory exists
    if (!fs.existsSync(DATA_DIR)) {
      fs.mkdirSync(DATA_DIR, { recursive: true });
    }

    // Create database connection
    db = new sqlite3.Database(DB_PATH);

    // Enable WAL mode for better concurrent access
    await runQuery(db, 'PRAGMA journal_mode = WAL');

    // Initialize tables
    await initializeTables();
  }

  return db;
}

async function initializeTables() {
  if (!db) return;

  // Create weather_data table with new schema
  const createWeatherTable = `
    CREATE TABLE IF NOT EXISTS weather_data (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      timestamp TEXT NOT NULL,
      station_lat REAL NOT NULL,
      station_long REAL NOT NULL,
      temperature REAL NOT NULL,
      wind_speed_meter_per_seconds REAL NOT NULL,
      is_raining INTEGER NOT NULL,
      sun_lux REAL NOT NULL,
      sun_twilight INTEGER NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `;

  await runQuery(db, createWeatherTable);

  // Create index on timestamp for faster queries
  const createTimestampIndex = `
    CREATE INDEX IF NOT EXISTS idx_weather_timestamp
    ON weather_data(timestamp)
  `;

  await runQuery(db, createTimestampIndex);

  // Create index on station location for location-based queries
  const createLocationIndex = `
    CREATE INDEX IF NOT EXISTS idx_weather_location
    ON weather_data(station_lat, station_long)
  `;

  await runQuery(db, createLocationIndex);
}

// Export query functions for use in API routes
export { runQuery, getQuery, allQuery };

// Graceful shutdown
export function closeDatabase(): Promise<void> {
  return new Promise((resolve) => {
    if (db) {
      db.close((err) => {
        if (err) console.error('Error closing database:', err);
        db = null;
        resolve();
      });
    } else {
      resolve();
    }
  });
}

// Handle process termination
process.on('exit', () => {
  if (db) {
    db.close();
  }
});

process.on('SIGINT', async () => {
  await closeDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await closeDatabase();
  process.exit(0);
});
